// components/teacher/TeacherStudentsPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import StudentManagementTabs from '../auth/teacher/students/student-management-tabs.client';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  teacherId: string;
  createdAt: string;
}

interface Teacher {
  id: string;
  name: string;
  email: string;
  slug: string;
}

interface TeacherStudentsPageProps {
  students: Student[];
  teacher: Teacher;
}

const TeacherStudentsPage = ({ students, teacher }: TeacherStudentsPageProps) => {
  const { slug } = useParams();
  const { user } = useAuth();

  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold">Loading...</h1>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-4">Student Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Manage your students and their class assignments.
        </p>
      </div>

      {/* Use the existing student management tabs component */}
      <StudentManagementTabs students={students} teacher={teacher} />
    </div>
  );
};

export default TeacherStudentsPage;
