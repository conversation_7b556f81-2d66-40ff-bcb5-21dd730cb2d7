// components/teacher/TeacherStudentsPageWithData.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import StudentManagementTabsWithData from '../auth/teacher/students/student-management-tabs-with-data.client';

interface User {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
  teacherId?: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  createdAt?: string;
}

interface TeacherStudentsPageWithDataProps {
  allUsers: User[];
  teacherSlug: string;
}

const TeacherStudentsPageWithData = ({ allUsers, teacherSlug }: TeacherStudentsPageWithDataProps) => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold">Loading...</h1>
      </div>
    );
  }

  // Find the teacher by slug
  const teacher = allUsers.find(u => u.slug === teacherSlug && u.role === 'teacher');
  
  if (!teacher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Teacher Not Found</h1>
          <p className="text-gray-600">The requested teacher profile could not be found.</p>
        </div>
      </div>
    );
  }

  // Filter students for this teacher
  const students = allUsers.filter(u => 
    u.teacherId === teacher.id && u.role === 'student'
  ).sort((a, b) => (a.name || '').localeCompare(b.name || ''));

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-4">Student Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Manage your students and their class assignments.
        </p>
      </div>

      {/* Use the student management tabs component with data */}
      <StudentManagementTabsWithData students={students} teacher={teacher} />
    </div>
  );
};

export default TeacherStudentsPageWithData;
