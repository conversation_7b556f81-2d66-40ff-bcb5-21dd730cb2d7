// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import TeacherStudentsPage from '../../../components/teacher/TeacherStudentsPage.client';

interface PageProps {
  params: { slug: string };
}

const TeachersStudentsPage = ({ params }: PageProps) => {
  const { slug } = params;

  // Fetch all users and filter on the client side for now
  const allUsers = use.users();

  // Filter to find the teacher by slug
  const teacher = allUsers?.find(user => user.slug === slug && user.role === 'teacher') || null;

  if (!teacher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Teacher Not Found</h1>
          <p className="text-gray-600">The requested teacher profile could not be found.</p>
        </div>
      </div>
    );
  }

  // Filter students for this teacher
  const students = allUsers?.filter(user =>
    user.teacherId === teacher.id && user.role === 'student'
  ).sort((a, b) => (a.name || '').localeCompare(b.name || '')) || [];

  return <TeacherStudentsPage students={students} teacher={teacher} />;
};

// Export as default for Blade framework
export default TeachersStudentsPage;
