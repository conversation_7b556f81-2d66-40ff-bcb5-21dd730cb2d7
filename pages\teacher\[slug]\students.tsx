// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import TeacherStudentsPage from '../../../components/teacher/TeacherStudentsPage.client';

interface PageProps {
  params: { slug: string };
}

const TeachersStudentsPage = ({ params }: PageProps) => {
  const { slug } = params;

  // Fetch the teacher by slug to get their ID
  const teachers = use.users({
    where: {
      slug: slug,
      role: 'teacher'
    }
  });

  const teacher = teachers?.[0];

  if (!teacher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Teacher Not Found</h1>
          <p className="text-gray-600">The requested teacher profile could not be found.</p>
        </div>
      </div>
    );
  }

  // Fetch students for this teacher
  const students = use.users({
    where: {
      teacherId: teacher.id,
      role: 'student'
    },
    orderBy: { name: 'asc' }
  });

  return <TeacherStudentsPage students={students || []} teacher={teacher} />;
};

// Export as default for Blade framework
export default TeachersStudentsPage;
