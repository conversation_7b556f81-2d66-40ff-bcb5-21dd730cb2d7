// pages/teacher/[slug]/students.tsx
import { use } from 'blade/server/hooks';
import TeacherStudentsPageWithData from '../../../components/teacher/TeacherStudentsPageWithData.client';

interface PageProps {
  params?: { slug?: string };
}

const TeachersStudentsPage = (props: PageProps) => {
  // Get the slug from the URL path - Blade should provide this
  const slug = props?.params?.slug;

  if (!slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Invalid URL</h1>
          <p className="text-gray-600">No teacher slug provided in the URL.</p>
        </div>
      </div>
    );
  }

  // Fetch all users from the database
  const allUsers = use.users();

  return <TeacherStudentsPageWithData allUsers={allUsers || []} teacherSlug={slug} />;
};

// Export as default for Blade framework
export default TeachersStudentsPage;
