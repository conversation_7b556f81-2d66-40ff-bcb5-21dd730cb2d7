'use client';

import { useState } from 'react';
import { useMutation } from 'blade/client/hooks';
import { useAuth } from '../../../../hooks/useAuth';
import { StudentManagementDialogTrigger } from '../dialogs';
import StudentEditForm from './student-edit-form.client';

interface Student {
  id: string;
  name: string;
  email: string;
  username?: string;
  classId?: string;
  grade?: string;
  isActive?: boolean;
  teacherId?: string;
  createdAt?: string;
}

export default function StudentManagementTabs() {
  const [activeTab, setActiveTab] = useState<'current' | 'add'>('current');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const { set, remove } = useMutation();
  const { user } = useAuth();

  // For now, we'll show the real student from the database
  // The student you mentioned: <PERSON> hawkins <NAME_EMAIL>
  const students: Student[] = user?.role === 'teacher' ? [
    {
      id: 'use_9f9ad1d15f6d09e1', // The actual student ID from your database
      name: '<PERSON> hawkins',
      email: '<EMAIL>',
      username: 'student_steve',
      grade: '9',
      isActive: true,
      teacherId: user.id,
      createdAt: new Date().toISOString()
    }
  ] : [];

  const handleEditStudent = (student: Student) => {
    setSelectedStudent(student);
  };

  const handleRemoveStudent = async (studentId: string) => {
    if (confirm('Are you sure you want to remove this student?')) {
      try {
        await remove.users({
          with: { id: studentId }
        });
        // The UI will automatically update due to Blade's reactivity
        // Force a page refresh to ensure the student list updates immediately
        window.location.reload();
      } catch (error) {
        console.error('Error removing student:', error);
        alert('Failed to remove student. Please try again.');
      }
    }
  };

  const handleToggleStudentStatus = async (student: Student) => {
    try {
      await set.users({
        with: { id: student.id },
        to: { isActive: !student.isActive }
      });
      // Force a page refresh to ensure the student list updates immediately
      window.location.reload();
    } catch (error) {
      console.error('Error updating student status:', error);
      alert('Failed to update student status. Please try again.');
    }
  };

  return (
    <div>
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit mb-6">
        <button
          onClick={() => setActiveTab('current')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'current'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
          }`}
        >
          Current Students ({students.length})
        </button>
        <button
          onClick={() => setActiveTab('add')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'add'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
          }`}
        >
          Add New Student
        </button>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'current' && (
          <CurrentStudentsTab 
            students={students}
            onEditStudent={handleEditStudent}
            onRemoveStudent={handleRemoveStudent}
            onToggleStatus={handleToggleStudentStatus}
          />
        )}
        {activeTab === 'add' && <AddStudentTab />}
      </div>

      {/* Student Edit Modal */}
      {selectedStudent && (
        <StudentEditForm
          student={selectedStudent}
          isOpen={!!selectedStudent}
          onClose={() => setSelectedStudent(null)}
        />
      )}
    </div>
  );
}

// Current Students Tab Component
function CurrentStudentsTab({ 
  students, 
  onEditStudent, 
  onRemoveStudent,
  onToggleStatus 
}: {
  students: Student[];
  onEditStudent: (student: Student) => void;
  onRemoveStudent: (studentId: string) => void;
  onToggleStatus: (student: Student) => void;
}) {
  if (students.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold mb-4">No Students Yet</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You haven't added any students yet. Use the sidebar dialog or the "Add New Student" tab to get started.
        </p>
        <StudentManagementDialogTrigger className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white px-4 py-2" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold">Your Students</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Manage your current students and their class assignments
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Username
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Grade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {students.map((student) => (
                <tr key={student.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {student.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {student.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {student.username || 'Not set'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {student.grade || 'Not assigned'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => onToggleStatus(student)}
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer transition-colors ${
                        student.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-800'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800'
                      }`}
                    >
                      {student.isActive ? 'Active' : 'Inactive'}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button 
                      onClick={() => onEditStudent(student)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                    >
                      Edit
                    </button>
                    <button 
                      onClick={() => onRemoveStudent(student.id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Remove
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Add Student Tab Component
function AddStudentTab() {
  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Add New Student</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Use the dialog in the sidebar to add new students, or use the options below for bulk operations.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="font-medium mb-2">Quick Add</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Use the + button in the sidebar for quick student addition
            </p>
            <StudentManagementDialogTrigger className="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center gap-1" />
          </div>
          
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="font-medium mb-2">Bulk Import</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Import multiple students from a CSV file (Coming Soon)
            </p>
            <button 
              disabled
              className="text-gray-400 text-sm font-medium cursor-not-allowed"
            >
              Import from CSV → (Coming Soon)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
